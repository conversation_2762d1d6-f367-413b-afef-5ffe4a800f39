<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ChangelogPanel from '@/components/shared/ChangelogPanel.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useLogsStore } from '@/stores/logs';
  import { MethodStatus, useMethodsStore, type MethodVersions } from '@/stores/method/methods';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useDebounceFn } from '@vueuse/core';
  import { SampleStatus, useSamplesStore, SampleType, type Sample } from '@/stores/sample/samples';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import { AnalysisStatus } from '@/stores/forms';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, onMounted, ref, watch, onBeforeUnmount, onUnmounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import ParametersSampleTable from './components/ParametersSampleTable.vue';
  import SampleTable from './components/SampleTable.vue';
  import SampleMethodRename from './components/NameMethod.vue';
  import RelatedAnalyticalRequests from './components/RelatedAnalyticalRequests.vue';
  import { useInstrumentsStore } from '@/stores/instruments';
  import { useColumnsStore } from '@/stores/columns';
  import { isAllowed, isAllowedAny } from '@/utils/directive/isAllowed';
  import { toTimeLocale } from '@/utils/locales';
  import { setPageTitle } from '@/utils/title';

  const instrumentsStore = useInstrumentsStore();

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const baseDataLoaded = ref(false);
  const relatedSamples = ref<Sample[]>([]);

  const route = useRoute();
  const router = useRouter();
  const logsStore = useLogsStore();
  const samplesStore = useSamplesStore();
  const { sample, loading, modalOptions } = storeToRefs(samplesStore);
  const instrumentName = ref('Přístroj');
  const kolonaName = ref('Kolona');
  const methodsStore = useMethodsStore();
  const columnsStore = useColumnsStore();
  const {
    instruments,
    loading: instrumentLoading,
    search: searchInstrument,
    fixedFilterOptions: instrumentOptions
  } = storeToRefs(instrumentsStore);
  const {
    options: columnsOption,
    loading: colonsLoading,
    totalItems,
    items: kolons,
    search: searchKolumn,
    fixedFilterOptions: optionsColumns
  } = storeToRefs(columnsStore);
  const {
    methodGets: methods,
    loading: methodLoading,
    search: searchTechnic,
    fixedFilterOptions: optionsMethods,
    methodVersions
  } = storeToRefs(methodsStore);
  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const tempMethod = ref<MethodVersions>();
  onMounted(async () => {
    await checkPermision();
    await showChangelog();
    await loadExecute();
    if (tempMethod.value) {
      showDetailedParams.value = false;
    }
    if (!isReadOnly.value && havePermision.value) {
      saveEvery5Minutes();
    }
  });

  const findAndSetGradient = () => {
    if (modalOptions.value?.updateData && modalOptions.value?.updateData.parameters) {
      const gradientParameter = modalOptions.value?.updateData.parameters.find(
        (p) => p.parameter === 'Gradient'
      );
      if (gradientParameter) {
        gradient.value = gradientParameter.value;
        modalOptions.value.updateData.parameters = modalOptions.value.updateData.parameters.filter(
          (p) => p.parameter !== 'Gradient'
        );
      }
    }
  };

  const gradient = ref<string>('');
  const havePermisionForChannelog = ref<boolean>(false);
  const showChangelog = async () => {
    if (isAllowed(['view_changelog'])) {
      havePermisionForChannelog.value = true;
    } else {
      havePermisionForChannelog.value = false;
    }
  };
  const checkPermision = async () => {
    if (isAllowed(['fill_rd_analyses']) && typeOfSample.value === 'rd') {
      havePermision.value = true;
    } else if (isAllowed(['fill_qc_vt_analyses']) && typeOfSample.value === 'qc-a-vt') {
      havePermision.value = true;
    } else {
      missingPermison.value =
        typeOfSample.value === 'rd' ? 'fill_rd_analyses' : 'fill_qc_vt_analyses';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte dostanečné oprávnení pro vytváření nových analytických requestů pro vzorky: ' +
          missingPermison.value +
          '.'
      });
    }
  };
  const chapterSearch = ref<string | undefined>(undefined);

  const sample_id = computed(() => route.params.sample_id as string);
  const sampleId = computed(() => parseInt(sample_id.value));

  const typeOfSample = computed(() => {
    switch (route.params.type) {
      case 'rd':
        return 'rd';
      case 'qc-a-vt':
        return 'qc-a-vt';
      default:
        return 'rd';
    }
  });

  const fetchRelatedSamples = async () => {
    if (!sample.value?.sample_number) {
      relatedSamples.value = [];
      return;
    }

    try {
      const filterOptions = [
        {
          column: 'sample_number',
          value: sample.value.sample_number
        }
      ];
      const originalFilterOptions = samplesStore.options.filterOptions;
      samplesStore.options.filterOptions = filterOptions;
      const { data } = await samplesStore.getSamples(false);
      samplesStore.options.filterOptions = originalFilterOptions;
      relatedSamples.value = data;
    } catch (error) {
      relatedSamples.value = [];
    }
  };

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (sampleId.value) {
      const _sample = await samplesStore.getSample(sampleId.value);
      if (_sample) {
        sample.value = _sample;
        samplesStore.setEditPage(sample.value.sample_id);
        if (sample.value?.sample_number) {
          setPageTitle(sample.value.sample_number);
        }
      }

      if (
        modalOptions.value?.updateData &&
        (!modalOptions.value.updateData.sequence_name ||
          modalOptions.value.updateData.sequence_name === '') &&
        shouldPrefillSequence.value
      ) {
        modalOptions.value.updateData.sequence_name = generateSequenceName();
      }
    } else {
      notification.error({
        message: 'Vzorek nebyl nalezen',
        description: 'Byl jste přesměrován na seznam vzorků'
      });
      router.push({ name: 'Samples', params: { type: typeOfSample.value }, hash: '#reload' });
    }

    columnsStore.setParamsFromLocation();
    instrumentsStore.setParamsFromLocation();
    methodsStore.setParamsFromLocation();
    if (
      sample.value?.technique_id &&
      sample.value?.analytical_request?.project_department?.project_id
    ) {
      optionsMethods.value = [
        {
          column: 'method__technique_id',
          value: sample.value?.technique_id
        },
        {
          column: 'method__project_id',
          value: sample.value?.analytical_request?.project_department?.project_id
        },
        {
          column: 'is_default_for_prefill',
          value: 'true'
        }
      ];
      await methodsStore.getMethodVersionsForSamples(true, {
        search: '',
        totalItems: 0,
        options: { page: 1, rowsPerPage: 25, sortBy: [] },
        fixedFilterOptions: optionsMethods.value,
        search_columns: ['method__method_name', 'description', 'branch__name', 'version_id']
      });
    } else if (sample.value?.technique_id) {
      optionsMethods.value = [
        {
          column: 'method__technique_id',
          value: sample.value?.technique_id
        },
        {
          column: 'is_default_for_prefill',
          value: 'true'
        }
      ];
      await methodsStore.getMethodVersionsForSamples(true, {
        search: '',
        totalItems: 0,
        options: { page: 1, rowsPerPage: 25, sortBy: [] },
        fixedFilterOptions: optionsMethods.value,
        search_columns: ['method__method_name', 'description', 'branch__name', 'version_id']
      });
    }

    if (tempTechnique.value && tempTechnique.value?.analytical_technique_id) {
      const techniqueIds = [];

      if (sample.value?.sub_technique?.analytical_technique_id) {
        techniqueIds.push(sample.value.sub_technique.analytical_technique_id);
      }
      if (sample.value?.technique?.analytical_technique_id) {
        techniqueIds.push(sample.value.technique.analytical_technique_id);
      }

      if (techniqueIds.length > 0) {
        optionsColumns.value = [
          {
            column: 'technique_id',
            value: techniqueIds.length === 1 ? techniqueIds[0] : techniqueIds
          }
        ];

        if (tempTechnique.value?.using_column) {
          await columnsStore.getColumns({
            search: '',
            totalItems: 0,
            options: { page: 1, rowsPerPage: 25, sortBy: [] },
            fixedFilterOptions: optionsColumns.value,
            search_columns: ['name', 'manufacturer']
          });
        }
      }
    }
    if (sample.value?.technique?.analytical_technique_id) {
      instrumentOptions.value = [
        {
          column: 'status',
          value: MethodStatus.ACTIVE
        },
        {
          column: 'technique_id',
          value: sample.value?.technique?.analytical_technique_id
        }
      ];
      await instrumentsStore.getInstruments({
        search: '',
        totalItems: 0,
        options: { page: 1, rowsPerPage: 25, sortBy: [] },
        fixedFilterOptions: instrumentOptions.value,
        search_columns: ['instrument_shortcut']
      });
      if (kolons.value) {
        if (!kolons.value.find((kolona) => kolona.kolona_id === sample.value?.kolona_id)) {
          if (sample.value?.kolona_id !== null) {
            const res = await columnsStore.getColumn(sample.value?.kolona_id);
            if (res && sample.value?.analytical_request?.project_department?.project_id) {
              kolons.value.push({
                kolona_id: res.kolona_id,
                technique: res.technique!,
                technique_id: res.technique_id!,
                name: res.name,
                chemistry: res.chemistry,
                diameter: res.diameter,
                length: res.length,
                particles: res.particles,
                serial_number: res.serial_number,
                catalog_number: res.catalog_number,
                manufacturer: res.manufacturer,
                used_since: res.used_since,
                note: res.note,
                status: res.status
              });
            }
          }
        }
      }
      if (instruments.value) {
        if (
          !Array.from(instruments.value.values()).find(
            (instrument) => instrument.instrument_id === sample.value?.instrument_id
          )
        ) {
          if (sample.value?.instrument_id !== null) {
            const res = await instrumentsStore.getInstrument(sample.value?.instrument_id);
            if (res) {
              instruments.value.set(res.instrument_id, res);
            }
          }
        }
      }
    }
    if (methodVersions.value) {
      await findMethodVersion(sample.value?.version_id);
    }
    findAndSetGradient();

    // Fetch related samples with the same sample number
    await fetchRelatedSamples();

    baseDataLoaded.value = true;
  };

  const shouldPrefillSequence = computed(() => {
    if (sample.value?.sub_technique !== null) {
      return sample.value?.sub_technique?.prefill_sample_sequence_name === true;
    }
    return sample.value?.technique?.prefill_sample_sequence_name === true;
  });

  const generateSequenceName = () => {
    if (sample.value?.analytical_request?.project_department?.project?.name) {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;
      return `${formattedDate} ${sample.value.analytical_request.project_department.project.name}`;
    }
    return '';
  };

  const findMethodVersion = async (versionId: number | undefined | null) => {
    if (!versionId) {
      return undefined;
    }
    if (methodVersions.value) {
      tempMethod.value = methodVersions.value.find((version) => version.version_id === versionId);
    }
    return undefined;
  };

  const loadExecuteWithoutVersions = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (sampleId.value) {
      const _sample = await samplesStore.getSample(sampleId.value);
      if (_sample) {
        sample.value = _sample;
        samplesStore.setEditPage(sample.value.sample_id);
      }

      if (
        modalOptions.value?.updateData &&
        (!modalOptions.value.updateData.sequence_name ||
          modalOptions.value.updateData.sequence_name === '') &&
        shouldPrefillSequence.value
      ) {
        modalOptions.value.updateData.sequence_name = generateSequenceName();
      }
    } else {
      notification.error({
        message: 'Vzorek nebyl nalezen',
        description: 'Byl jste přesměrován na seznam vzorků'
      });
      router.push({ name: 'Samples', params: { type: typeOfSample.value }, hash: '#reload' });
    }

    columnsStore.setParamsFromLocation();
    instrumentsStore.setParamsFromLocation();
    methodsStore.setParamsFromLocation();

    if (tempTechnique.value && tempTechnique.value?.analytical_technique_id) {
      const techniqueIds = [];

      if (sample.value?.sub_technique?.analytical_technique_id) {
        techniqueIds.push(sample.value.sub_technique.analytical_technique_id);
      }
      if (sample.value?.technique?.analytical_technique_id) {
        techniqueIds.push(sample.value.technique.analytical_technique_id);
      }

      if (techniqueIds.length > 0) {
        optionsColumns.value = [
          {
            column: 'technique_id',
            value: techniqueIds.length === 1 ? techniqueIds[0] : techniqueIds
          }
        ];

        if (tempTechnique.value?.using_column) {
          await columnsStore.getColumns({
            search: '',
            totalItems: 0,
            options: { page: 1, rowsPerPage: 25, sortBy: [] },
            fixedFilterOptions: optionsColumns.value,
            search_columns: ['name', 'manufacturer']
          });
        }
      }
    }
    if (sample.value?.technique?.analytical_technique_id) {
      instrumentOptions.value = [
        {
          column: 'status',
          value: MethodStatus.ACTIVE
        },
        {
          column: 'technique_id',
          value: sample.value?.technique?.analytical_technique_id
        }
      ];
      await instrumentsStore.getInstruments({
        search: '',
        totalItems: 0,
        options: { page: 1, rowsPerPage: 25, sortBy: [] },
        fixedFilterOptions: instrumentOptions.value,
        search_columns: ['instrument_shortcut']
      });
      if (kolons.value) {
        if (!kolons.value.find((kolona) => kolona.kolona_id === sample.value?.kolona_id)) {
          if (sample.value?.kolona_id !== null) {
            const res = await columnsStore.getColumn(sample.value?.kolona_id);
            if (res && sample.value?.analytical_request?.project_department?.project_id) {
              kolons.value.push({
                kolona_id: res.kolona_id,
                technique: res.technique!,
                technique_id: res.technique_id!,
                name: res.name,
                chemistry: res.chemistry,
                diameter: res.diameter,
                length: res.length,
                particles: res.particles,
                serial_number: res.serial_number,
                catalog_number: res.catalog_number,
                manufacturer: res.manufacturer,
                used_since: res.used_since,
                note: res.note,
                status: res.status
              });
            }
          }
        }
      }
      if (instruments.value) {
        if (
          !Array.from(instruments.value.values()).find(
            (instrument) => instrument.instrument_id === sample.value?.instrument_id
          )
        ) {
          if (sample.value?.instrument_id !== null) {
            const res = await instrumentsStore.getInstrument(sample.value?.instrument_id);
            if (res) {
              instruments.value.set(res.instrument_id, res);
            }
          }
        }
      }
    }
    if (methodVersions.value) {
      await findMethodVersion(sample.value?.version_id);
    }
    findAndSetGradient();
    baseDataLoaded.value = true;
  };

  const loadAndRedirect = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (sampleId.value) {
      const _sample = await samplesStore.getSample(sampleId.value);
      if (_sample) {
        sample.value = _sample;
      }
    }
    if (sample.value) {
      samplesStore.setEditPage(sample.value.sample_id);
      baseDataLoaded.value = true;
      router.push({ name: 'Samples', params: { type: typeOfSample.value }, hash: '#reload' });
    }
  };

  watch([sample_id], () => {
    loadExecute();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Seznam vzorků',
        disabled: false,
        href: router.resolve({ name: 'Samples', params: { type: typeOfSample.value } }).href
      },
      {
        title: sample.value?.sample_number ?? 'Detail vzorku',
        disabled: true,
        href: router.resolve({
          name: 'SampleEdit',
          params: { type: typeOfSample.value, sample_id: sampleId.value }
        }).href
      }
    ];
  });

  const reloadPageWithoutSave = () => {
    loadExecuteWithoutVersions();
  };

  const CreateSyntheticTemplateForm = ref();
  const isInProgress = ref(false);
  async function submitFormToValidate() {
    if (
      CreateSyntheticTemplateForm.value.isValid &&
      modalOptions.value?.updateData &&
      !isInProgress.value
    ) {
      isInProgress.value = true;
      modalOptions.value.updateData.files_ids = [...modalOptions.value.updateData.files_ids];
      modalOptions.value.method_id = tempMethod.value?.method_id;
      modalOptions.value.updateData.version_id = tempMethod.value?.version_id;
      if (gradient.value.length > 0) {
        modalOptions.value.updateData.parameters.push({
          parameter: 'Gradient',
          value: extractTextFromHtml(gradient.value)
        });
      }
      const res = await samplesStore.updateSample();
      if (res) {
        if (sample.value?.status === SampleStatus.DONE) {
          editModeForDoneSample.value = false;
        }
        reloadPageWithoutSave();
      }
    }
    isInProgress.value = false;
  }
  const havePermisionLogs = ref<boolean>(true);
  const missingPermisonLogs = ref<string>();
  const checkPermisionLogs = () => {
    if (isAllowed(['view_changelog'])) {
      havePermisionLogs.value = true;
      return true;
    } else {
      missingPermisonLogs.value = 'view_changelog';
      havePermisionLogs.value = false;
    }
    if (!havePermisionLogs.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte oprávnění pro prohlížení changelogu: ' + missingPermisonLogs.value + '.'
      });
    }
    return false;
  };

  const showLogs = () => {
    if (checkPermisionLogs()) {
      if (sample.value) {
        const config = [
          {
            table_name: ['sample'],
            table_primary_key: [sample.value.sample_id]
          },
          sample.value?.parameters.length
            ? {
                table_name: ['parametr'],
                table_primary_key: sample.value.parameters.map((p) => p.parameter_id)
              }
            : {},
          sample.value?.files.length
            ? {
                table_name: ['file'],
                table_primary_key: sample.value.files.map((file) => file.file_id)
              }
            : {}
        ];
        logsStore.setMultiColumnsAsActiveFilter(config);
      } else {
        notification.error({ message: 'Nepodařilo se načíst data pro zobrazení změn' });
      }

      logsStore.showDrawer = true;
    }
  };

  const methodSearch = ref('');

  watch(methodSearch, () => {
    debouncedProjectTechniqueSearch();
  });

  const debouncedProjectTechniqueSearch = useDebounceFn(async () => {
    if (methodLoading.value === false && methodSearch.value !== '' && methodSearch.value) {
      searchTechnic.value = methodSearch.value;
      if (
        sample.value?.technique_id &&
        sample.value?.analytical_request?.project_department?.project_id
      ) {
        optionsMethods.value = [
          {
            column: 'method__technique_id',
            value: sample.value?.technique_id
          },
          {
            column: 'method__project_id',
            value: sample.value?.analytical_request?.project_department?.project_id
          },
          {
            column: 'is_default_for_prefill',
            value: 'true'
          }
        ];
        await methodsStore.getMethodVersionsForSamples(true, {
          search: methodSearch.value,
          totalItems: 0,
          options: { page: 1, rowsPerPage: 25, sortBy: [] },
          fixedFilterOptions: optionsMethods.value,
          search_columns: ['method__method_name', 'description', 'branch__name', 'version_id']
        });
      }
    }
  }, 350);

  const saveAndReanalyzeSample = async () => {
    if (!isInProgress.value && modalOptions.value?.updateData) {
      isInProgress.value = true;
      modalOptions.value.updateData.files_ids = [...modalOptions.value.updateData.files_ids];
      modalOptions.value.method_id = tempMethod.value?.method_id;
      modalOptions.value.updateData.version_id = tempMethod.value?.version_id;
      if (gradient.value.length > 0) {
        modalOptions.value.updateData.parameters.push({
          parameter: 'Gradient',
          value: extractTextFromHtml(gradient.value)
        });
      }
      const res = await samplesStore.saveAndReanalyzeSample();
      if (res) {
        loadAndRedirect();
      }
    }
    isInProgress.value = false;
  };

  const cancelSampleAnalysis = async () => {
    const res = await samplesStore.cancelSampleAnalysis();
    if (res) {
      loadAndRedirect();
    }
  };

  const saveSampleAsNewMethod = async (method_name: string) => {
    showNameMethodModal.value = false;
    const res = await samplesStore.saveSampleAsNewMethod(method_name);
    if (res) {
      reloadPageWithoutSave();
    }
  };
  const showNameMethodModal = ref(false);
  const toggleNameMenthodeModal = async () => {
    showNameMethodModal.value = !showNameMethodModal.value;
  };
  const saveAndReportSample = async () => {
    if (!isInProgress.value && modalOptions.value?.updateData) {
      isInProgress.value = true;
      modalOptions.value.updateData.files_ids = [...modalOptions.value.updateData.files_ids];
      modalOptions.value.method_id = tempMethod.value?.method_id;
      modalOptions.value.updateData.version_id = tempMethod.value?.version_id;
      if (gradient.value.length > 0) {
        modalOptions.value.updateData.parameters.push({
          parameter: 'Gradient',
          value: extractTextFromHtml(gradient.value)
        });
      }
      const res = await samplesStore.saveAndReportSample();
      if (res) {
        loadAndRedirect();
      }
    }
    isInProgress.value = false;
  };

  const editModeForDoneSample = ref(false);
  const havePermisionToEditDoneSample = computed(() => {
    return isAllowedAny(['edit_all', 'edit_finished_sample']);
  });

  const toggleEditModeForDoneSample = () => {
    if (sample.value?.status === SampleStatus.DONE) {
      if (havePermisionToEditDoneSample.value) {
        editModeForDoneSample.value = true;
      } else {
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nemáte oprávnění pro úpravu dokončeného vzorku.'
        });
      }
    }
  };

  const isReadOnly = computed(() => {
    if (sample.value?.status === SampleStatus.DONE) {
      return !editModeForDoneSample.value;
    }
    return (
      sample.value?.status === SampleStatus.CANCELLED || sample.value?.type === SampleType.EXTERNAL
    );
  });

  const addFileToForm = (file_id: number) => {
    if (modalOptions.value?.updateData) {
      modalOptions.value.updateData.files_ids.push(file_id);
    }
  };

  const removeFileFromForm = (file_id: number) => {
    if (modalOptions.value?.updateData !== undefined) {
      modalOptions.value.updateData.files_ids = modalOptions.value?.updateData.files_ids.filter(
        (f) => f !== file_id
      );
    }
  };

  const instrumentSearch = ref('');
  const kolonaSearch = ref('');
  watch(instrumentSearch, () => {
    debouncedProjectInstrumentSearch();
  });
  watch(kolonaSearch, () => {
    debouncedSearchKolumn();
  });
  const debouncedProjectInstrumentSearch = useDebounceFn(() => {
    if (instrumentLoading.value === false && instrumentSearch.value !== '') {
      if (sample.value?.technique?.analytical_technique_id) {
        searchInstrument.value = instrumentSearch.value;
        instrumentOptions.value = [
          {
            column: 'status',
            value: MethodStatus.ACTIVE
          },
          {
            column: 'technique_id',
            value: sample.value?.technique?.analytical_technique_id
          }
        ];
        instrumentsStore.getInstruments({
          search: '',
          totalItems: 0,
          options: { page: 1, rowsPerPage: 25, sortBy: [] },
          fixedFilterOptions: instrumentOptions.value,
          search_columns: ['instrument_shortcut']
        });
      }
    }
  }, 350);

  const debouncedSearchKolumn = useDebounceFn(() => {
    if (colonsLoading.value === false && kolonaSearch.value !== '') {
      searchKolumn.value = kolonaSearch.value;
      if (tempTechnique.value && tempTechnique.value?.analytical_technique_id) {
        const techniqueIds = [];

        if (sample.value?.sub_technique?.analytical_technique_id) {
          techniqueIds.push(sample.value.sub_technique.analytical_technique_id);
        }

        if (sample.value?.technique?.analytical_technique_id) {
          techniqueIds.push(sample.value.technique.analytical_technique_id);
        }

        if (techniqueIds.length > 0) {
          optionsColumns.value = [
            {
              column: 'technique_id',
              value: techniqueIds.length === 1 ? techniqueIds[0] : techniqueIds
            }
          ];
          columnsStore.getColumns({
            search: kolonaSearch.value,
            totalItems: 0,
            options: { page: 1, rowsPerPage: 25, sortBy: [] },
            fixedFilterOptions: optionsColumns.value,
            search_columns: ['name', 'manufacturer']
          });
        }
      }
    }
  }, 350);

  onBeforeUnmount(() => {
    columnsStore.search = '';
    instrumentsStore.search = '';
    methodsStore.search = '';
  });
  const exportSample = async () => {
    const res = await samplesStore.exportSample(sampleId.value, sample.value?.sample_number);
    if (res) {
      reloadPageWithoutSave();
    }
  };
  const exportSampleToWord = async () => {
    const res = await samplesStore.exportSampleToWord(sampleId.value, sample.value?.sample_number);
    if (res) {
      reloadPageWithoutSave();
    }
  };
  const isUpdateDataSameAsOriginal = computed(() => {
    let parametersCheck = true;
    if (baseDataLoaded.value === false) {
      return true;
    }

    const gradientCheck = gradient.value ? true : false;

    if (modalOptions.value?.updateData && sample.value) {
      let updateDataParametersLength = modalOptions.value?.updateData.parameters?.length || 0;
      const sampleParametersLength = sample.value.parameters?.length || 0;

      if (gradientCheck) {
        updateDataParametersLength += 1;
      }

      if (updateDataParametersLength !== sampleParametersLength) {
        parametersCheck = false;
      } else if (updateDataParametersLength > 0 && sampleParametersLength > 0) {
        for (let i = 0; i < modalOptions.value.updateData.parameters.length; i++) {
          if (
            sample.value.parameters[i].parameter !==
              modalOptions.value.updateData.parameters[i].parameter ||
            sample.value.parameters[i].value !== modalOptions.value.updateData.parameters[i].value
          ) {
            parametersCheck = false;
          }
        }
      }
      return (
        modalOptions.value.updateData.sequence_name === sample.value.sequence_name &&
        modalOptions.value.updateData.instrument_id === sample.value.instrument_id &&
        modalOptions.value.updateData.kolona_id === sample.value.kolona_id &&
        modalOptions.value.updateData.preparation_of_standard_and_sample ===
          sample.value.preparation_of_standard_and_sample &&
        modalOptions.value.updateData.note_or_specification ===
          sample.value.note_or_specification &&
        modalOptions.value.updateData.result === sample.value.result &&
        updateDataParametersLength === sampleParametersLength &&
        parametersCheck
      );
    }

    return true;
  });

  const backButtonProps = computed(() => {
    if (sample.value?.status === SampleStatus.DONE && editModeForDoneSample.value) {
      return {
        color: 'warning',
        text: 'Zpět'
      };
    }
    return {
      color: 'error',
      text: isUpdateDataSameAsOriginal.value || isReadOnly.value ? 'Zpět' : 'Zrušit'
    };
  });

  const showDetailedParams = ref(true);

  const onMethodSelected = async (selected: MethodVersions | null) => {
    if (selected && modalOptions.value?.updateData && selected.data) {
      showDetailedParams.value = false;
      if (kolons.value) {
        if (
          !kolons.value.find((kolona) => kolona.kolona_id === tempMethod.value?.data?.kolona_id)
        ) {
          if (tempMethod.value?.data?.kolona_id !== null) {
            const res = await columnsStore.getColumn(tempMethod.value?.data?.kolona_id);
            if (res && sample.value?.analytical_request?.project_department?.project_id) {
              kolons.value.push({
                kolona_id: res.kolona_id,
                technique: res.technique!,
                technique_id: res.technique_id!,
                name: res.name,
                chemistry: res.chemistry,
                diameter: res.diameter,
                length: res.length,
                particles: res.particles,
                serial_number: res.serial_number,
                catalog_number: res.catalog_number,
                manufacturer: res.manufacturer,
                used_since: res.used_since,
                note: res.note,
                status: res.status
              });
            }
          }
        }
      }
      if (instruments.value) {
        if (
          !Array.from(instruments.value.values()).find(
            (instrument) => instrument.instrument_id === tempMethod.value?.data?.instrument_id
          )
        ) {
          if (tempMethod.value?.data?.instrument_id !== null) {
            const res = await instrumentsStore.getInstrument(tempMethod.value?.data?.instrument_id);
            if (res) {
              instruments.value.set(res.instrument_id, res);
            }
          }
        }
      }

      modalOptions.value.updateData.instrument_id = selected.data.instrument_id;
      modalOptions.value.updateData.kolona_id = selected.data.kolona_id;
      modalOptions.value.updateData.preparation_of_standard_and_sample =
        selected.data.preparation_of_standard_and_sample;
      modalOptions.value.updateData.parameters = selected.data.parameters ?? [];
      if (selected.data.gradient.length > 0) {
        gradient.value = selected.data.gradient;
      } else {
        gradient.value = '';
      }
    }
  };

  const extractTextFromHtml = (htmlString: string): string => {
    const div = document.createElement('div');
    div.innerHTML = htmlString;
    return div.textContent || div.innerText || '';
  };
  /*
  watch(
    () => modalOptions.value?.updateData,
    (newData) => {
      if (newData) {
        localStorage.setItem('sampleStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
  */
  //if subtechnique is not null, use subtechnique else use technique
  //this apply to using_column and analytical_technique_id
  const tempTechnique = computed(() => {
    if (sample.value) {
      if (sample.value.sub_technique) {
        return sample.value.sub_technique;
      } else {
        return sample.value.technique;
      }
    }
    return null;
  });

  let saveInterval: number;

  const saveEvery5Minutes = () => {
    saveInterval = setInterval(async () => {
      //if (!isUpdateDataSameAsOriginal.value) {
      await submitFormToValidate();
      // }
    }, 300000);
  };

  onUnmounted(() => {
    clearInterval(saveInterval);
  });

  const backButtonColor = computed(() => {
    if (sample.value?.status === SampleStatus.DONE && editModeForDoneSample.value) {
      return 'warning';
    }
    return 'error';
  });

  const backButtonText = computed(() => {
    if (sample.value?.status === SampleStatus.DONE && editModeForDoneSample.value) {
      return 'Zpět';
    }
    return isUpdateDataSameAsOriginal.value || isReadOnly.value ? 'Zpět' : 'Zrušit';
  });
</script>

<template>
  <LoaderWrapper
    v-if="!sample || modalOptions?.updateData === undefined || modalOptions?.baseData === undefined"
  />
  <template v-else>
    <TopPageBreadcrumb title="Vzorek" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded || loading">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="8">
                <div
                  v-if="
                    modalOptions.baseData.type && modalOptions.baseData.type === SampleType.INTERNAL
                  "
                  class="d-flex gap-2 justify-start flex-wrap"
                >
                  <v-btn
                    v-if="sample?.status !== SampleStatus.DONE"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="isReadOnly || !havePermision"
                    @click.prevent.stop="saveAndReanalyzeSample"
                  >
                    Reanalyzovat
                  </v-btn>
                  <v-btn
                    v-if="sample?.status !== SampleStatus.DONE"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="isReadOnly || !havePermision"
                    @click.prevent.stop="saveAndReportSample"
                  >
                    Reportovat
                  </v-btn>
                  <v-btn
                    v-if="sample?.status !== SampleStatus.DONE"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="isReadOnly || !havePermision"
                    @click.prevent.stop="toggleNameMenthodeModal"
                  >
                    Uložit jako metodu
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!havePermisionForChannelog"
                    @click.prevent="showLogs"
                  >
                    Změny
                  </v-btn>
                  <v-btn
                    v-if="sample?.status !== SampleStatus.DONE"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="isReadOnly || !havePermision"
                    @click.prevent="
                      async () => {
                        if (
                          await ConfirmRef?.open('Potvrzení', 'Opravdu chcete zrušit analýzu?', {
                            color: 'error',
                            notclosable: true,
                            zIndex: 2400
                          })
                        ) {
                          cancelSampleAnalysis();
                        }
                      }
                    "
                  >
                    Zrušení analýzy
                  </v-btn>
                </div>
              </v-col>
              <v-col cols="12" md="4">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    :color="backButtonProps.color"
                    :disabled="!baseDataLoaded || !havePermision"
                    @click.prevent="
                      async () => {
                        if (sample?.status === SampleStatus.DONE && editModeForDoneSample) {
                          editModeForDoneSample = false;
                          reloadPageWithoutSave();
                          return;
                        }

                        if (!isUpdateDataSameAsOriginal) {
                          if (!isReadOnly) {
                            if (
                              await ConfirmRef?.open(
                                'Potvrzení',
                                'Opravdu chcete zrušit změny a načíst data ze serveru?',
                                {
                                  color: 'error',
                                  notclosable: true,
                                  zIndex: 2400
                                }
                              )
                            ) {
                              reloadPageWithoutSave();
                            }
                          } else {
                            router.push({ name: 'Samples', params: { type: typeOfSample } });
                          }
                        } else {
                          router.push({ name: 'Samples', params: { type: typeOfSample } });
                        }
                      }
                    "
                  >
                    {{ backButtonProps.text }}
                  </v-btn>
                  <v-btn
                    v-if="isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || !havePermision"
                    @click.prevent="exportSample"
                  >
                    Exportovat do PDF
                  </v-btn>
                  <v-btn
                    v-if="isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || !havePermision"
                    @click.prevent="exportSampleToWord"
                  >
                    Exportovat do Wordu
                  </v-btn>

                  <v-btn
                    v-if="sample?.status === SampleStatus.DONE && !editModeForDoneSample"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || !havePermisionToEditDoneSample"
                    @click.prevent="toggleEditModeForDoneSample"
                  >
                    Povolit úpravy
                  </v-btn>

                  <v-btn
                    v-if="!isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || !havePermision"
                    type="submit"
                    form="experiment-edit-form"
                  >
                    Uložit
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <v-form
            id="experiment-edit-form"
            ref="CreateSyntheticTemplateForm"
            :readonly="isReadOnly || !havePermision"
            @submit.prevent="submitFormToValidate"
          >
            <v-row>
              <v-col cols="12" md="6">
                <SampleTable :sample="sample" />
              </v-col>
              <v-col cols="12" md="6">
                <RelatedAnalyticalRequests
                  :current-sample="sample"
                  :related-samples="relatedSamples"
                />
              </v-col>
            </v-row>
            <v-row v-if="sample?.type === SampleType.INTERNAL" class="mt-5">
              <v-col cols="12" md="6">
                <v-label class="mb-2">Název sekvence</v-label>
                <v-text-field
                  v-model="modalOptions.updateData.sequence_name"
                  single-line
                  placeholder="Zadejte název sekvence"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Analytická metoda</v-label>
                <v-autocomplete
                  v-if="methodVersions"
                  v-model="tempMethod"
                  v-model:search="methodSearch"
                  :items="
                    methodVersions
                      .filter((version) => version.is_default_for_prefill)
                      .map((version) => {
                        return {
                          value: version,
                          title: `${version.method?.method_name} (${version.branch.name}) - ${version.description} (${toTimeLocale(version.created_at)})`,
                          version: version
                        };
                      })
                  "
                  hide-details
                  rounded="sm"
                  variant="outlined"
                  color="primary"
                  label="Vyberte analytickou metodu"
                  single-line
                  class="autocomplete"
                  :clearable="!isReadOnly"
                  :no-data-text="'Žádná další políčka'"
                  :slim="true"
                  :loading="methodLoading"
                  @update:model-value="onMethodSelected"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.version.method?.method_name }} ({{
                            item.raw.version.branch.name
                          }}) -
                          {{ item.raw.version.description }}
                        </h6>
                        <small class="text-h6 text-lightText">
                          Vytvořena: {{ toTimeLocale(item.raw.version.created_at) }}
                        </small>
                      </div>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Parametr</v-label>
                <v-text-field
                  v-model="instrumentName"
                  :rules="itemRequiredRule"
                  single-line
                  placeholder="Zadejte název sekvence"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Přístroj</v-label>
                <v-autocomplete
                  v-model="modalOptions.updateData.instrument_id"
                  v-model:search="instrumentSearch"
                  hide-details
                  :rules="isReadOnly ? [] : itemRequiredRule"
                  rounded="sm"
                  :clearable="!isReadOnly"
                  :items="
                    [...instruments.values()].map((instrument) => {
                      return {
                        value: instrument.instrument_id,
                        title: `${instrument.instrument_shortcut} (${instrument.instrument_id})`,
                        instrument: instrument
                      };
                    })
                  "
                  variant="outlined"
                  color="primary"
                  label="Vyberte přístroj"
                  single-line
                  class="autocomplete"
                  :no-data-text="'Žádná další políčka'"
                  :slim="true"
                  :loading="instrumentLoading"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.instrument.instrument_shortcut }} ({{
                            item.raw.instrument.instrument_id
                          }})
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>

              <template v-if="tempTechnique && tempTechnique.using_column">
                <v-col cols="12" md="6">
                  <v-label class="mb-2">Parametr</v-label>
                  <v-text-field
                    v-model="kolonaName"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název sekvence"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    :readonly="true"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <v-label class="mb-2">Kolona</v-label>
                  <v-autocomplete
                    v-if="tempTechnique && tempTechnique.analytical_technique_id"
                    v-model="modalOptions.updateData.kolona_id"
                    v-model:search="kolonaSearch"
                    hide-details
                    :rules="isReadOnly ? [] : itemRequiredRule"
                    :clearable="!isReadOnly"
                    rounded="sm"
                    :items="
                      [...kolons.values()].map((kolona) => {
                        return {
                          value: kolona.kolona_id,
                          title: `${kolona.name} (${kolona.manufacturer})`,
                          kolona: kolona
                        };
                      })
                    "
                    variant="outlined"
                    color="primary"
                    label="Vyberte kolonu"
                    single-line
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                    :loading="colonsLoading"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>

                    <template #item="{ props, item }">
                      <v-list-item v-bind="props" :title="''">
                        <div class="player-wrapper pa-2">
                          <h6 class="text-subtitle-1 mb-0">
                            {{ item.raw.kolona.name }} ({{ item.raw.kolona.manufacturer }})
                          </h6>
                        </div>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                </v-col>
              </template>
              <v-col cols="12" class="d-flex justify-center mt-3">
                <v-btn
                  color="primary"
                  variant="outlined"
                  :prepend-icon="showDetailedParams ? 'mdi-chevron-up' : 'mdi-chevron-down'"
                  @click="showDetailedParams = !showDetailedParams"
                >
                  {{ showDetailedParams ? 'Skrýt parametry' : 'Zobrazit parametry' }}
                </v-btn>
              </v-col>

              <template v-if="showDetailedParams">
                <v-col cols="12">
                  <ParametersSampleTable
                    v-model="modalOptions.updateData.parameters"
                    :is-read-only="isReadOnly || !havePermision"
                  />
                </v-col>
              </template>
              <template v-if="baseDataLoaded">
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Gradient</v-label>
                  <EditorTextarea
                    v-model="gradient"
                    :disabled="isReadOnly || !havePermision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-2">Příprava standardu a vzorku</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.preparation_of_standard_and_sample"
                    :disabled="isReadOnly || !havePermision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Poznámka</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.note_or_specification"
                    :disabled="isReadOnly || !havePermision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Výsledek</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.result"
                    :disabled="isReadOnly || !havePermision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Přílohy</v-label>
                  <template v-if="isReadOnly || !havePermision">
                    <FileUploader
                      v-model="sample.files"
                      :disabled="true"
                      :process-save-file="addFileToForm"
                      :process-remove-file="removeFileFromForm"
                      :uppy-options="{
                        height: 250,
                        restrictions: {
                          maxNumberOfFiles: 10,
                          minNumberOfFiles: 0,
                          allowedFileTypes: null
                        }
                      }"
                    />
                  </template>
                  <template v-else>
                    <FileUploader
                      v-model="sample.files"
                      :process-save-file="addFileToForm"
                      :process-remove-file="removeFileFromForm"
                      :uppy-options="{
                        height: 250,
                        restrictions: {
                          maxNumberOfFiles: 10,
                          minNumberOfFiles: 0,
                          allowedFileTypes: null
                        }
                      }"
                    />
                  </template>
                </v-col>
              </template>
            </v-row>
            <v-row v-else-if="sample?.type === SampleType.EXTERNAL">
              <template v-if="baseDataLoaded">
                <v-col cols="12" style="margin-top: 2em">
                  <v-label class="mb-2">Specifikace</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.note_or_specification"
                    :disabled="isReadOnly || !havePermision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Výsledek</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.result"
                    :disabled="isReadOnly || !havePermision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>

                <v-col cols="12">
                  <v-radio-group v-model="modalOptions.baseData.analysis_status">
                    <v-radio label="Vyhovuje" :value="AnalysisStatus.COMPLIANT"></v-radio>
                    <v-radio label="Nevyhovuje" :value="AnalysisStatus.NON_COMPLIANT"></v-radio>
                    <v-radio label="N/A" :value="AnalysisStatus.NA"></v-radio>
                  </v-radio-group>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Přílohy</v-label>
                  <template v-if="isReadOnly || !havePermision">
                    <FileUploader
                      v-model="sample.files"
                      :disabled="true"
                      :process-save-file="addFileToForm"
                      :process-remove-file="removeFileFromForm"
                      :uppy-options="{
                        height: 250,
                        restrictions: {
                          maxNumberOfFiles: 10,
                          minNumberOfFiles: 0,
                          allowedFileTypes: null
                        }
                      }"
                    />
                  </template>
                  <template v-else>
                    <FileUploader
                      v-model="sample.files"
                      :process-save-file="addFileToForm"
                      :process-remove-file="removeFileFromForm"
                      :uppy-options="{
                        height: 250,
                        restrictions: {
                          maxNumberOfFiles: 10,
                          minNumberOfFiles: 0,
                          allowedFileTypes: null
                        }
                      }"
                    />
                  </template>
                </v-col>
              </template>
            </v-row>

            <FileSection
              v-if="baseDataLoaded"
              :is-read-only="isReadOnly || !havePermision"
              :files="sample.files"
              :file-search="undefined"
              @reload="loadExecute"
              @file-remove="
                (file_id: number) =>
                  modalOptions?.updateData?.files_ids.filter((f) => f !== file_id)
              "
            >
              <template #notFound>Nebyly nalezeny žádné soubory</template>
            </FileSection>
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <ChangelogPanel v-if="logsStore.showDrawer" v-model:show="logsStore.showDrawer" />
      <SampleMethodRename
        v-if="baseDataLoaded"
        v-model:show="showNameMethodModal"
        @update-name="saveSampleAsNewMethod"
        @reload="loadExecute"
      />
    </v-row>
  </template>
</template>
