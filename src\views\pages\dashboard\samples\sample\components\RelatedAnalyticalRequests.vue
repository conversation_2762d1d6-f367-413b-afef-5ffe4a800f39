<template>
  <v-card v-if="relatedRequests.length > 0" class="mt-4">
    <v-card-title class="text-h6 pb-2">
      Ostatní analytické p<PERSON> se stejným číslem vzorky
    </v-card-title>
    <v-card-text class="pt-0">
      <v-table density="compact">
        <thead>
          <tr>
            <th class="text-left font-weight-bold">Název</th>
            <th class="text-left font-weight-bold">Technika</th>
            <th class="text-left font-weight-bold">Status</th>
            <th class="text-left font-weight-bold">Akce</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="request in relatedRequests" :key="request.sample_id">
            <td>{{ request.name || '/' }}</td>
            <td>{{ request.technique_name || '/' }}</td>
            <td>
              <v-chip :color="getStatusColor(request.status)" size="small" label>
                {{ getStatusText(request.status) }}
              </v-chip>
            </td>
            <td>
              <router-link
                :to="{
                  name: 'SampleEdit',
                  params: {
                    sample_id: request.sample_id,
                    type: request.sample_type
                  }
                }"
                target="_blank"
                class="text-decoration-none"
              >
                <v-btn
                  size="small"
                  variant="outlined"
                  color="primary"
                  prepend-icon="mdi-open-in-new"
                >
                  Otevřít
                </v-btn>
              </router-link>
            </td>
          </tr>
        </tbody>
      </v-table>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Sample } from '@/stores/sample/samples';

  interface RelatedRequest {
    analytical_request_id: number;
    name: string;
    technique_name: string;
    status: string;
    sample_id: number;
    sample_type: string;
  }

  interface Props {
    currentSample?: Sample | null;
    relatedSamples?: Sample[];
  }

  const props = defineProps<Props>();

  const relatedRequests = computed<RelatedRequest[]>(() => {
    if (!props.relatedSamples || props.relatedSamples.length === 0) {
      return [];
    }

    // Transform Sample objects to RelatedRequest format
    return props.relatedSamples.map((sample) => ({
      analytical_request_id: sample.analytical_request_id,
      name: sample.analytical_request?.name || sample.name || 'N/A',
      technique_name: sample.technique?.name || 'N/A',
      status: sample.analytical_request?.status || sample.status || 'unknown',
      sample_id: sample.sample_id,
      sample_type: sample.type || 'rd' // Default to 'rd' if type is not available
    }));
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'created':
        return 'primary';
      case 'in_progress':
        return 'warning';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'grey';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'created':
        return 'Vytvořeno';
      case 'in_progress':
        return 'Probíhá';
      case 'completed':
        return 'Dokončeno';
      case 'cancelled':
        return 'Zrušeno';
      default:
        return status;
    }
  };
</script>

<style scoped>
  .v-table th {
    font-weight: 600 !important;
  }

  .v-table td {
    padding: 8px 16px !important;
  }

  .v-card-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
  }
</style>
