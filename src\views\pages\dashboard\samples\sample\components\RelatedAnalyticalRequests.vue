<template>
  <v-card v-if="relatedRequests.length > 0" class="mt-4">
    <v-card-title class="text-h6 pb-2">
      Ostatní analytické p<PERSON> se stejným číslem vzorky
    </v-card-title>
    <v-card-text class="pt-0">
      <v-table density="compact">
        <thead>
          <tr>
            <th class="text-left font-weight-bold">Název</th>
            <th class="text-left font-weight-bold">Technika</th>
            <th class="text-left font-weight-bold">Status</th>
            <th class="text-left font-weight-bold">Akce</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="request in relatedRequests" :key="request.sample_id">
            <td>{{ request.name || '/' }}</td>
            <td>{{ request.technique_name || '/' }}</td>
            <td>
              <v-chip :color="getStatusColor(request.status)" size="small" label>
                {{ getStatusText(request.status) }}
              </v-chip>
            </td>
            <td>
              <router-link
                :to="{
                  name: 'SampleDetail',
                  params: {
                    sample_id: request.sample_id,
                    type: request.sample_type
                  },
                  query: getTransitiveRelatedSamplesQuery(request)
                }"
                target="_blank"
                class="text-decoration-none"
              >
                <v-btn
                  size="small"
                  variant="outlined"
                  color="primary"
                  prepend-icon="mdi-open-in-new"
                >
                  Otevřít
                </v-btn>
              </router-link>
            </td>
          </tr>
        </tbody>
      </v-table>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { Sample } from '@/stores/sample/samples';

  interface RelatedRequest {
    analytical_request_id: number;
    name: string;
    technique_name: string;
    status: string;
    sample_id: number;
    sample_type: string;
  }

  interface Props {
    currentSample?: Sample | null;
  }

  const props = defineProps<Props>();
  const route = useRoute();

  const relatedRequests = computed<RelatedRequest[]>(() => {
    const relatedSamplesParam = route.query.related_samples;
    if (!relatedSamplesParam || typeof relatedSamplesParam !== 'string') {
      return [];
    }

    try {
      const parsed = JSON.parse(relatedSamplesParam);
      return Array.isArray(parsed) ? parsed : [];
    } catch (error) {
      console.error('Error parsing related samples:', error);
      return [];
    }
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'created':
        return 'primary';
      case 'in_progress':
        return 'warning';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'grey';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'created':
        return 'Vytvořeno';
      case 'in_progress':
        return 'Probíhá';
      case 'completed':
        return 'Dokončeno';
      case 'cancelled':
        return 'Zrušeno';
      default:
        return status;
    }
  };

  const getTransitiveRelatedSamplesQuery = (targetRequest: RelatedRequest) => {
    // Get current sample ID from route params
    const currentSampleId = parseInt(route.params.sample_id as string);

    // Create a new related samples list that includes:
    // 1. All current related samples (excluding the target we're clicking to)
    // 2. The current sample (the one we're navigating away from)
    const newRelatedSamples = [
      // Add all current related samples except the target we're clicking to
      ...relatedRequests.value.filter((req) => req.sample_id !== targetRequest.sample_id),
      // Add the current sample we're viewing
      ...(props.currentSample && relatedRequests.value.length > 0
        ? [
            {
              analytical_request_id: props.currentSample.analytical_request_id,
              name: props.currentSample.analytical_request?.name || 'N/A',
              technique_name: props.currentSample.technique?.name || 'N/A',
              status: props.currentSample.analytical_request?.status || 'unknown',
              sample_id: currentSampleId,
              sample_type: route.params.type as string
            }
          ]
        : [])
    ];

    if (newRelatedSamples.length === 0) {
      return {};
    }

    return {
      related_samples: JSON.stringify(newRelatedSamples)
    };
  };
</script>

<style scoped>
  .v-table th {
    font-weight: 600 !important;
  }

  .v-table td {
    padding: 8px 16px !important;
  }

  .v-card-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
  }
</style>
