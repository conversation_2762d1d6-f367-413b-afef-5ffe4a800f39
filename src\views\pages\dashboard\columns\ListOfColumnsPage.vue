<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { roundTableData, useColumnsStore } from '@/stores/columns';
  import { useProjectsStore } from '@/stores/projects';
  import { Technique, TechniqueType, useTechniquesStore } from '@/stores/techniques';
  import { toLocale } from '@/utils/locales';
  import { EditOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { onBeforeRouteUpdate, useRouter } from 'vue-router';
  import type { Header, Item, ClickRowArgument } from 'vue3-easy-data-table';
  import ColumnModal from './components/ColumnModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const router = useRouter();
  const techniquesStore = useTechniquesStore();
  const { loading: techniqueLoading } = storeToRefs(techniquesStore);

  const projectsStore = useProjectsStore();
  const { loading: projectLoading } = storeToRefs(projectsStore);

  const columnsStore = useColumnsStore();
  const { options, loading, totalItems, items, search } = storeToRefs(columnsStore);

  const loadFromServer = async () => {
    columnsStore.getAll();
  };

  const debouncedSearch = useDebounceFn(() => {
    if (loading.value === false) loadFromServer();
  }, 350);

  const baseDataLoaded = ref(false);
  const havePermision = ref<boolean>(false);
  const missingPermison = ref<string>();
  onMounted(async () => {
    await checkPermision();
    columnsStore.setParamsFromLocation();
    debouncedSearch();
    projectsStore.getAll();
    techniquesStore.getAll([TechniqueType.EXTERNAL, TechniqueType.INTERNAL]);
    baseDataLoaded.value = true;
  });
  const checkPermision = async () => {
    if (isAllowed(['add_edit_columns'])) {
      havePermision.value = true;
    } else {
      missingPermison.value = 'add_edit_columns';
      havePermision.value = false;
    }
  };
  watch(
    [options, totalItems],
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  watch(search, () => {
    if (loading.value === false) debouncedSearch();
  });

  onBeforeRouteUpdate((to, from, next) => {
    if (to.path === from.path && to.query !== from.query) {
      columnsStore.setParamsFromLocation();
      debouncedSearch();
    }

    next();
  });

  onBeforeUnmount(() => {
    columnsStore.search = '';
  });

  const headers: Header[] = [
    { text: 'ID', value: 'kolona_id', sortable: true },
    { text: 'Technika', value: 'technique', sortable: false },
    { text: 'Název', value: 'name', sortable: true },
    { text: 'Stacionární fáze', value: 'chemistry', sortable: true },
    { text: 'Průměr', value: 'diameter', sortable: true },
    { text: 'Délka', value: 'length', sortable: true },
    { text: 'Částice', value: 'particles', sortable: true },
    { text: 'Sériové číslo', value: 'serial_number', sortable: true },
    { text: 'Katalogové číslo', value: 'catalog_number', sortable: true },
    { text: 'Výrobce', value: 'manufacturer', sortable: true },
    { text: 'Používáno od', value: 'used_since', sortable: false },
    { text: 'Poznámka', value: 'note', sortable: false },
    { text: 'Stav', value: 'status', sortable: true },
    { text: 'Akce', value: 'action' }
  ];

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Seznam kolon',
        disabled: true,
        href: router.resolve({ name: 'SeznamKolon' }).href
      }
    ];
  });

  const showRow = (item: ClickRowArgument) => {
    columnsStore.showPreviewModal(item.kolona_id, 'one');
  };
</script>
<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb title="Seznam kolon" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card elevation="0" variant="outlined" class="withbg pageSize">
          <v-card-item>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat kolonu"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!havePermision"
                    @click.prevent="columnsStore.showNewColumnModal()"
                  >
                    Přidat kolonu
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text>
            <CustomTable
              v-model:server-options="options"
              :server-items-length="totalItems"
              :loading="loading || techniqueLoading || projectLoading"
              :headers="headers"
              :items="items"
              multi-sort
              @click-row="showRow"
            >
              <template #item-technique="{ technique }: { technique: Technique }">
                <div class="player-wrapper">
                  <h6 class="text-subtitle-1 mb-0">
                    {{ technique.name }} ({{ technique.shortcut }}) -
                    <v-chip v-if="technique.status === 'active'" color="success" size="small" label>
                      Aktivní
                    </v-chip>
                    <v-chip
                      v-if="technique.status === 'inactive'"
                      color="warning"
                      size="small"
                      label
                    >
                      Neaktivní
                    </v-chip>
                    <v-chip v-if="technique.status === 'deleted'" color="error" size="small" label>
                      Odstraněno
                    </v-chip>
                  </h6>
                  <small class="text-h6 text-lightText">
                    Typ: {{ technique.type }} | Vytvořena: {{ toLocale(technique.created_at) }}
                  </small>
                </div>
              </template>

              <template #item-diameter="{ diameter }">{{ roundTableData(diameter) }}</template>
              <template #item-length="{ length }">{{ roundTableData(length) }}</template>

              <template #item-used_since="{ used_since }">{{ toLocale(used_since) }}</template>

              <template #item-status="{ status }">
                <v-chip v-if="status === 'active'" color="success" size="small" label>
                  Aktivní
                </v-chip>
                <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                  Deaktivováno
                </v-chip>
              </template>

              <template #item-action="{ kolona_id }">
                <div class="operation-wrapper">
                  <v-btn
                    icon
                    color="secondary"
                    variant="text"
                    rounded="sm"
                    @click.stop.prevent="columnsStore.showPreviewModal(kolona_id, 'one')"
                  >
                    <EyeOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    color="primary"
                    variant="text"
                    rounded="sm"
                    :disabled="!havePermision"
                    @click.stop.prevent="columnsStore.showEditModal(kolona_id)"
                  >
                    <EditOutlined />
                  </v-btn>
                  <!-- <v-btn
                  v-if="status !== 'deleted'"
                  icon
                  color="error"
                  variant="text"
                  rounded="sm"
                  @click.prevent="
                    async () => {
                      if (await ConfirmRef?.open('Potvrzení', 'Opravdu chcete smazat kolonu?', { color: 'error', notclosable: true })) {
                        columnsStore.deleteColumn(kolona_id);
                      }
                    }
                  "
                >
                  <DeleteOutlined />
                </v-btn> -->
                </div>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <ColumnModal
        v-if="columnsStore.showColumnModal"
        v-model:show="columnsStore.showColumnModal"
      />
    </v-row>
  </template>
</template>
<style lang="css" scoped>
  :deep() tbody tr :hover {
    cursor: pointer;
  }
</style>
