<script setup lang="ts">
  import CustomNumberInput from '@/components/shared/CustomNumberInput.vue';
  import TechniquesSelect from '@/components/shared/TechniquesSelect.vue';
  import { useColumnsStore } from '@/stores/columns';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { formatDate } from '@/utils/locales';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref, onMounted, watch } from 'vue';
  import type { Header } from 'vue3-easy-data-table';
  import { toLocale } from '@/utils/locales';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  const tab = ref('one');
  onMounted(() => {
    if (modalOptions.value?.defaultTab) {
      tab.value = modalOptions.value.defaultTab;
    }
  });
  const columnsStore = useColumnsStore();
  const { modalOptions, loading, historyItems: items, historyLength } = storeToRefs(columnsStore);
  const CreateColumnForm = ref();
  async function submitFormToValidate() {
    if (CreateColumnForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return columnsStore.updateColumn();
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return columnsStore.createColumn();

        default:
          return 'Náhled kolony';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled kolony';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace kolony';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nová kolona';
      default:
        return 'Náhled kolony';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit kolunu';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat kolunu';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });

  const headers: Header[] = [
    { text: 'Číslo vzorku', value: 'sample_number', sortable: false },
    { text: 'Přístroj', value: 'instrument', sortable: false },
    { text: 'Analytická metoda', value: 'method_name', sortable: false },
    { text: 'Datum vytvoření', value: 'created_at', sortable: false }
  ];

  watch(
    () => tab.value,
    async (newTab, oldTab) => {
      if (newTab) {
        if (newTab === 'three') {
          if (await checkPermision()) {
            await columnsStore.getChapterHistoryOfUsage();
          } else {
            tab.value = oldTab;
          }
        }
      }
    }
  );

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkPermision = async () => {
    if (isAllowed(['view_column_statistics'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'view_column_statistics';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte dostanečné oprávnení pro prohlížení historie kolon: ' +
          missingPermison.value +
          '.'
      });
    }
    return false;
  };

  watch(
    () => modalOptions.value?.newData,
    (newData) => {
      if (newData && modalOptions.value?.isCreating) {
        localStorage.setItem('columnsStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateColumnForm"
        class="createColumnForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text v-if="!onlyPreview">
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Technika</v-label>
                  <TechniquesSelect
                    v-model:selected-techniques="modalOptions.newData.technique_id"
                    :readonly="onlyPreview"
                    :clearable="!onlyPreview"
                    :rules="itemRequiredRule"
                    :filter-options="[
                      { column: 'type', value: ['internal'] },
                      { column: 'status', value: 'active' },
                      { column: 'using_column', value: 'true' }
                    ]"
                  ></TechniquesSelect>
                </v-col>

                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Název</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Stacionární fáze</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.chemistry"
                    :rules="itemRequiredRule"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Průměr (mm)</v-label>
                  <CustomNumberInput
                    v-model="modalOptions.newData.diameter"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    min="0"
                  />
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Délka (mm)</v-label>
                  <CustomNumberInput
                    v-model="modalOptions.newData.length"
                    :is-required="true"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    min="0"
                  />
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Částice (um)</v-label>
                  <CustomNumberInput
                    v-model="modalOptions.newData.particles"
                    :is-required="true"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    min="0"
                  />
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">SN</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.serial_number"
                    :rules="itemRequiredRule"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Cat. No.</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.catalog_number"
                    :rules="itemRequiredRule"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">Výrobce</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.manufacturer"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte výrobce"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-label class="mb-2">
                    Používáno od: {{ formatDate(modalOptions.newData.used_since) }}
                  </v-label>
                  <v-date-input
                    v-model="modalOptions.newData.used_since"
                    clearable
                    density="compact"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    cancel-text="Zrušit"
                    ok-text="Potvrdit"
                    prepend-icon=""
                  ></v-date-input>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-2">Poznámka</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.note"
                    single-line
                    placeholder="Zadejte poznámku"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col v-if="!modalOptions.isCreating" cols="12">
                  <v-label class="mb-2">Status</v-label>
                  <v-autocomplete
                    v-model="modalOptions.newData.status"
                    :readonly="onlyPreview"
                    :items="[
                      { value: 'active', title: 'Aktivní' },
                      { value: 'deleted', title: 'Deaktivovat' }
                    ]"
                    rounded="sm"
                    color="primary"
                    single-line
                    hide-details
                    variant="outlined"
                    :no-data-text="'Žádná další políčka'"
                  ></v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-text v-else>
          <v-card flat>
            <v-tabs v-model="tab" density="compact" class="mb-6">
              <v-tab value="one">Náhled kolony</v-tab>
              <v-tab value="three">Historie použití</v-tab>
            </v-tabs>

            <v-card-text class="pa-0">
              <v-tabs-window v-model="tab">
                <v-tabs-window-item value="one">
                  <v-row>
                    <v-col>
                      <v-row>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Technika</v-label>
                          <TechniquesSelect
                            v-model:selected-techniques="modalOptions.newData.technique_id"
                            :readonly="onlyPreview"
                            :clearable="!onlyPreview"
                            :rules="itemRequiredRule"
                            :filter-options="[
                              { column: 'type', value: ['internal'] },
                              { column: 'status', value: 'active' },
                              { column: 'using_column', value: 'true' }
                            ]"
                          ></TechniquesSelect>
                        </v-col>

                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Název</v-label>
                          <v-text-field
                            v-model="modalOptions.newData.name"
                            :rules="itemRequiredRule"
                            single-line
                            placeholder="Zadejte název"
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Stacionární fáze</v-label>
                          <v-text-field
                            v-model="modalOptions.newData.chemistry"
                            :rules="itemRequiredRule"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Průměr (mm)</v-label>
                          <CustomNumberInput
                            v-model="modalOptions.newData.diameter"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                            min="0"
                          />
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Délka (mm)</v-label>
                          <CustomNumberInput
                            v-model="modalOptions.newData.length"
                            :is-required="true"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                            min="0"
                          />
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Částice (um)</v-label>
                          <CustomNumberInput
                            v-model="modalOptions.newData.particles"
                            :is-required="true"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                            min="0"
                          />
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">SN</v-label>
                          <v-text-field
                            v-model="modalOptions.newData.serial_number"
                            :rules="itemRequiredRule"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Cat. No.</v-label>
                          <v-text-field
                            v-model="modalOptions.newData.catalog_number"
                            :rules="itemRequiredRule"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">Výrobce</v-label>
                          <v-text-field
                            v-model="modalOptions.newData.manufacturer"
                            :rules="itemRequiredRule"
                            single-line
                            placeholder="Zadejte výrobce"
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-label class="mb-2">
                            Používáno od: {{ formatDate(modalOptions.newData.used_since) }}
                          </v-label>
                          <v-date-input
                            v-model="modalOptions.newData.used_since"
                            clearable
                            density="compact"
                            single-line
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                            cancel-text="Zrušit"
                            ok-text="Potvrdit"
                            prepend-icon=""
                          ></v-date-input>
                        </v-col>
                        <v-col cols="12">
                          <v-label class="mb-2">Poznámka</v-label>
                          <v-text-field
                            v-model="modalOptions.newData.note"
                            single-line
                            placeholder="Zadejte poznámku"
                            hide-details="auto"
                            variant="outlined"
                            rounded="sm"
                          ></v-text-field>
                        </v-col>

                        <v-col v-if="!modalOptions.isCreating" cols="12">
                          <v-label class="mb-2">Status</v-label>
                          <v-autocomplete
                            v-model="modalOptions.newData.status"
                            :readonly="onlyPreview"
                            :items="[
                              { value: 'active', title: 'Aktivní' },
                              { value: 'deleted', title: 'Deaktivovat' }
                            ]"
                            rounded="sm"
                            color="primary"
                            single-line
                            hide-details
                            variant="outlined"
                            :no-data-text="'Žádná další políčka'"
                          ></v-autocomplete>
                        </v-col>
                        <v-col v-if="modalOptions.isCreating" cols="12">
                          <div class="d-flex justify-space-between gap-2">
                            <div class="pb-4">
                              <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                            </div>
                            <v-switch
                              v-model="modalOptions.newData.confirm"
                              color="primary"
                              class="switchRight"
                              hide-details
                            ></v-switch>
                          </div>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-tabs-window-item>
                <v-tabs-window-item value="three">
                  <v-divider></v-divider>
                  <v-card-text class="mb-2">
                    <CustomTable
                      :loading="loading"
                      :headers="headers"
                      :items="[...items.values()]"
                      :show-header="false"
                      :show-index="false"
                      :hide-footer="false"
                    >
                      <template #item-sample_number="{ sample_number }">{{ sample_number }}</template>
                      <template #item-instrument="{ instrument }">
                        {{ instrument.instrument_shortcut }}
                      </template>
                      <template #item-method_name="{ method_name }">{{ method_name }}</template>
                      <template #item-created_at="{ created_at }">
                        {{ toLocale(created_at) }}
                      </template>
                    </CustomTable>
                  </v-card-text>
                </v-tabs-window-item>
              </v-tabs-window>
            </v-card-text>
          </v-card>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="columnsStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<style scoped>
  :deep(.v-tabs-window) {
    overflow: hidden !important;
  }
</style>
