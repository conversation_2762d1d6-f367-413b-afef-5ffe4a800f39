<template>
  <LoaderWrapper v-if="newGenerateAnalyticalRequestData === null" />
  <template v-else>
    <TopPageBreadcrumb title="Nová analýza" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn variant="flat" color="error" @click.prevent="reloadPageWithoutSave">
                    Zpět
                  </v-btn>
                  <v-btn variant="flat" color="primary" type="submit" form="form-edit-form">
                    Odeslat analytický požadavek
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>

          <v-form
            v-if="baseDataLoaded"
            id="form-edit-form"
            ref="CreateSyntheticTemplateForm"
            @submit.prevent="submitFormToValidate"
          >
            <v-row class="mb-2">
              <v-col cols="12" md="6">
                <v-label class="mb-2">Zadavatel</v-label>
                <UsersSelect
                  v-model:selected-users="selectedUser"
                  :clearable="true"
                  :what-to-show="['first_name', 'last_name', 'user_email']"
                ></UsersSelect>
              </v-col>

              <v-spacer></v-spacer>
            </v-row>
            <v-row>
              <v-col sm="6" cols="12">
                <v-label class="mb-2">Projekt</v-label>
                <v-autocomplete
                  v-model="selectedProject"
                  v-model:search="searchValue"
                  :rules="itemRequiredRule"
                  rounded="sm"
                  :items="
                    allProjectsOptionsPage.all_projects.results
                      .filter((project) =>
                        project.departments?.some((department) => department.type === 'analytical')
                      )
                      .map((project) => ({
                        value: project,
                        title: `${project.name} (${project.getShortcuts})`,
                        project: project
                      }))
                  "
                  variant="outlined"
                  color="primary"
                  label="Vyberte projekt"
                  single-line
                  class="autocomplete"
                  :no-data-text="'Žádná další políčka'"
                  :slim="true"
                  :loading="loadingProject"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.project.name }} ( {{ item.raw.project.getShortcuts }} )
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col sm="6" cols="12">
                <v-label class="mb-2">Oddělení</v-label>
                <v-autocomplete
                  v-model="selectedDepartment"
                  :rules="itemRequiredRule"
                  rounded="sm"
                  :items="
                    selectedProject && selectedProject.departments
                      ? selectedProject.departments.map((department) => {
                          return {
                            value: department,
                            title: `${department.shortcut} ( ${department.type} ) `,
                            department: department
                          };
                        })
                      : []
                  "
                  variant="outlined"
                  color="primary"
                  :label="departmentLabel()"
                  single-line
                  class="autocomplete"
                  :no-data-text="'Žádná další políčka'"
                  :slim="true"
                  :readonly="!selectedProject"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.department.shortcut }} ( {{ item.raw.department.type }} )
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>
            </v-row>
            <v-row v-if="selectedDepartment?.type === DepartmentEnum.ANALYTICAL" class="mb-2">
              <v-col cols="12" md="6">
                <v-label class="mb-2">Analyzovat jako standard</v-label>
                <v-radio-group
                  v-model="standardType.standard_type"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  inline
                  :readonly="!isDepartmentAnalytical"
                >
                  <v-radio label="RD" :value="'rd'"></v-radio>
                  <v-radio label="QC" :value="'qc'"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col
                v-if="
                  selectedDepartment?.type === DepartmentEnum.ANALYTICAL &&
                  standardType.standard_type === 'rd'
                "
                md="6"
                cols="12"
                class="mb-2"
              >
                <v-label class="mb-2">Zvolte standard</v-label>
                <v-autocomplete
                  v-if="standards"
                  v-model="selectedStandard"
                  :items="
                    standards
                      .filter((s) => s.status === StandardStatus.ACTIVE)
                      .map((standard) => {
                        return {
                          value: standard,
                          title: `${standard.standard_name}`,
                          standard: standard
                        };
                      })
                  "
                  :clearable="true"
                  hide-details
                  rounded="sm"
                  variant="outlined"
                  color="primary"
                  label="Vyberte standard"
                  single-line
                  class="autocomplete"
                  :no-data-text="'Žádná další políčka'"
                  :slim="true"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.standard.standard_name }}
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>
            </v-row>
            <v-row class="mb-2">
              <v-col cols="12" md="4">
                <v-label class="mb-2">Název vzorku</v-label>
                <v-text-field
                  v-model="newGenerateAnalyticalRequestData.name"
                  single-line
                  placeholder="Zadejte název vzorku"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :rules="itemRequiredRule"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="4">
                <v-label class="mb-2">Číslo šarže</v-label>
                <v-combobox
                  v-model="batchNumber"
                  v-model:search="searchValueBatch"
                  :items="batches"
                  item-title="batch_number"
                  item-value="batch_number"
                  :clearable="false"
                  hide-details
                  rounded="sm"
                  variant="outlined"
                  color="primary"
                  label="Vyberte číslo šarže"
                  single-line
                  class="autocomplete no-dropdown-arrow"
                  :loading="loadingBatches"
                  :slim="true"
                  :allow-new="true"
                  @update:model-value="onSelectBatch"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.batch_number }}
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-combobox>
              </v-col>

              <v-col cols="12" md="4">
                <v-label class="mb-2">Číslo vzorku</v-label>
                <v-combobox
                  v-model="sampleNumber"
                  v-model:search="searchValueSample"
                  rounded="sm"
                  :items="analyticalRequests"
                  variant="outlined"
                  color="primary"
                  item-title="sample_number"
                  item-value="sample_number"
                  label="Vyberte číslo vzorku"
                  single-line
                  class="autocomplete"
                  :no-data-text="dynamicNoDataTextSample"
                  :slim="true"
                  :loading="loadingSamples"
                  :allow-new="true"
                  @update:model-value="onSelectSample"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.sample_number }}
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-combobox>
              </v-col>
            </v-row>
            <v-row class="mb-2">
              <v-col sm="12" cols="12">
                <v-label class="mb-2">Zvolte techniky</v-label>
                <NewParametersFormTable
                  v-model="newGenerateAnalyticalRequestData.samples"
                  :project-id="selectedProject?.project_id ? selectedProject.project_id : undefined"
                  :selected-department="selectedDepartment"
                ></NewParametersFormTable>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Přílohy</v-label>
                <FileUploader
                  v-model="newGenerateAnalyticalRequestData.files"
                  :uppy-options="{
                    restrictions: {
                      maxNumberOfFiles: 10,
                      minNumberOfFiles: 0,
                      allowedFileTypes: null
                    }
                  }"
                />
                <FileSection
                  :files="newGenerateAnalyticalRequestData.files"
                  :custom-remove-file="true"
                  @file-remove="
                    async (file_id: number) => {
                      const res = await filesStore.deleteFile(file_id);
                      if (res) {
                        getAllFiles(file_id);
                      }
                    }
                  "
                />
              </v-col>
            </v-row>
          </v-form>
        </UiParentCard>
      </v-col>
      <ConfirmDlg ref="ConfirmRef" />
      <UserModal v-if="usersStore.showUserModal" v-model:show="usersStore.showUserModal" />
    </v-row>
  </template>
</template>

<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import UsersSelect from '@/components/shared/UsersSelect.vue';
  import { User } from '@/stores/auth';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useUsersStore } from '@/stores/users';
  import { DepartmentEnum } from '@/stores/projects';
  import { type InsertNewAnalysisDto } from '@/stores/forms';
  import {
    NewAnalyticalRequestType,
    useAnalyticalRequestsStore,
    type AnalyticalRequestModalNewPostDataI,
    type BatchNumbersDto,
    type AnalyticalRequestDto
  } from '@/stores/analyticalRequests/analyticalRequests';
  import {
    useProjectsStore,
    type SimpleProject,
    type DepartmentDto,
    type GetAllOptions
  } from '@/stores/projects';
  import { useStandardsStore, type StandardDto, StandardStatus } from '@/stores/standard/standards';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, reactive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useDebounceFn } from '@vueuse/core';
  import NewParametersFormTable from '../components/ParametersSampleTableNewVersion.vue';
  import UserModal from '../components/CreateNewUserSample.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import { useFilesStore } from '@/stores/files';
  import { notification } from 'ant-design-vue';

  import { setPageTitle } from '@/utils/title';

  const filesStore = useFilesStore();
  const route = useRoute();
  const router = useRouter();
  const projectsStore = useProjectsStore();
  const {
    loading: loadingProject,
    allProjectsOptionsPage,
    projectTechniques
  } = storeToRefs(projectsStore);
  const usersStore = useUsersStore();
  const standardsStore = useStandardsStore();
  const { standards } = storeToRefs(standardsStore);
  const analyticalRequestsStore = useAnalyticalRequestsStore();
  const baseDataLoaded = ref(false);
  const selectedUser = ref<number | User | number[] | User[] | undefined>(undefined);
  const newGenerateAnalyticalRequestData = ref<InsertNewAnalysisDto | null>(null);
  const selectedProject = ref<SimpleProject | null>(null);
  const selectedDepartment = ref<DepartmentDto | null>(null);
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const selectedStandard = ref<StandardDto | null>(null);
  const SampleOptions = reactive<GetAllOptions>({
    search: '',
    totalItems: undefined,
    options: {
      page: 1,
      rowsPerPage: 25,
      sortBy: [],
      sortType: []
    },
    search_columns: ['sample_number'],
    fixedFilterOptions: []
  });
  const {
    batches,
    analyticalRequests,
    loadingBatches,
    loadingSamples,
    batchesOptions,
    options: sampleOptions
  } = storeToRefs(analyticalRequestsStore);
  const isDepartmentAnalytical = computed(() => {
    return selectedDepartment.value?.type === DepartmentEnum.ANALYTICAL;
  });
  interface newSampleData {
    standard_type: string | null;
  }

  const standardType = ref<newSampleData>({ standard_type: null });

  onMounted(async () => {
    await loadExecute();
    standardsStore.getStandards();

    baseDataLoaded.value = true;
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: pageTitle.value,
        disabled: false,
        href: router.resolve({ name: 'Samples' }).href
      }
    ];
  });

  const departmentLabel = () => {
    if (selectedProject.value) {
      return 'Vyberte oddělení';
    }
    return 'Vyberte nejdříve projekt';
  };

  const mapRouteTypeToRequestType = (type: string): 'QC-A-VT' | 'RD' => {
    if (type === 'rd') {
      return 'RD';
    } else if (type === 'qc-a-vt') {
      return 'QC-A-VT';
    }
    return 'RD';
  };

  const requestType = ref<'QC-A-VT' | 'RD'>(mapRouteTypeToRequestType(route.params.type as string));

  watch(
    () => route.params.type,
    (newType) => {
      requestType.value = mapRouteTypeToRequestType(newType as string);
    },
    { immediate: true }
  );

  const pageTitle = computed(() => {
    if (requestType.value === 'RD') {
      return 'Vzorky RD';
    } else {
      return 'Vzorky QC a VT';
    }
  });

  const loadExecute = async () => {
    const getLocalStorage = localStorage.getItem('newAnalyticalRequestExternalStore');
    let parsedData;
    if (getLocalStorage) {
      parsedData = JSON.parse(getLocalStorage);
    }
    newGenerateAnalyticalRequestData.value = {
      name: '',
      sample_number: null,
      samples: [],
      files_ids: [],
      files: [],
      batch_number: null
    };
    // selectedProject.value = parsedData?.project || null;
    // standardType.value.standard_type = parsedData?.analytical_department_request_type || null;
    // selectedDepartment.value = parsedData?.project_department || null;
    selectedUser.value = undefined;
    allProjectsOptionsPage.value.all_projects.search = '';
    setPageTitle(pageTitle.value);
  };

  watch(
    () => newGenerateAnalyticalRequestData.value?.files,
    () => {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.files_ids =
          newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);
      }
    }
  );

  watch(selectedProject, (newProject) => {
    if (!newProject) {
      selectedDepartment.value = null;
    } else {
      selectedDepartment.value = null;
      standardType.value.standard_type = null;
      selectedStandard.value = null;
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.sample_number = null;
        newGenerateAnalyticalRequestData.value.batch_number = null;
        newGenerateAnalyticalRequestData.value.samples = [];
      }
      batchNumber.value = null;
      sampleNumber.value = null;
      batches.value = [];
      analyticalRequests.value = [];
    }
  });

  watch(selectedDepartment, async (newDepartment) => {
    if (!newDepartment) {
      standardType.value.standard_type = null;
    } else {
      if (
        newGenerateAnalyticalRequestData.value &&
        selectedDepartment.value?.project_department_id
      ) {
        newGenerateAnalyticalRequestData.value.sample_number = null;
        standardType.value.standard_type = null;
      }
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.sample_number = null;
        newGenerateAnalyticalRequestData.value.batch_number = null;
        newGenerateAnalyticalRequestData.value.samples = [];
      }
      batchNumber.value = null;
      sampleNumber.value = null;
      batches.value = [];
      analyticalRequests.value = [];
    }
    if (selectedDepartment.value && selectedDepartment.value.project_department_id) {
      batchesOptions.value.search = '';
      batchesOptions.value.search_columns = ['batch_number'];
      batchesOptions.value.filterOptions = [
        { column: 'project_department_id', value: selectedDepartment.value.project_department_id }
      ];

      await loadBatchFromServer();
    }
  });

  const reloadPageWithoutSave = () => {
    loadExecute();
    router.push({
      name: 'Samples',
      params: {
        type: requestType.value.toLocaleLowerCase()
      }
    });
  };
  const CreateSyntheticTemplateForm = ref();
  const isInProgress = ref(false);

  async function submitFormToValidate() {
    if (
      CreateSyntheticTemplateForm.value.isValid &&
      newGenerateAnalyticalRequestData.value &&
      selectedDepartment.value &&
      selectedProject.value &&
      standardType.value &&
      selectedUser.value &&
      !isInProgress.value
    ) {
      isInProgress.value = true;
      const sampleNumber = newGenerateAnalyticalRequestData.value?.sample_number;
      if (!sampleNumber && ConfirmRef?.value) {
        const confirm = await ConfirmRef?.value.open(
          'Upozornění',
          'Nemáte vložené číslo vzorku. Číslo vzorku bude automaticky vygenerováno. Chcete pokračovat?',
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400,
            placement: 'top'
          }
        );
        if (!confirm) {
          return;
        }
      }
      newGenerateAnalyticalRequestData.value.files_ids =
        newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);
      const data = {
        project_id: selectedProject.value.project_id,
        type:
          standardType.value.standard_type === null
            ? NewAnalyticalRequestType.NORMAL
            : standardType.value.standard_type === 'rd'
              ? NewAnalyticalRequestType.STANDARD_RD
              : NewAnalyticalRequestType.STANDARD_QC,
        project_department_id: selectedDepartment.value.project_department_id,
        sample_number: newGenerateAnalyticalRequestData.value.sample_number,
        batch_number: newGenerateAnalyticalRequestData.value.batch_number,
        name: newGenerateAnalyticalRequestData.value.name,
        analytical_department_request_type: standardType.value.standard_type,
        samples: newGenerateAnalyticalRequestData.value.samples,
        files_ids: newGenerateAnalyticalRequestData.value.files_ids,
        user_id: selectedUser.value
      } as AnalyticalRequestModalNewPostDataI;

      const res = await analyticalRequestsStore.createAnalyticalRequest(data);
      if (res) {
        localStorage.removeItem('newAnalyticalRequestExternalStore');
        router.push({
          name: 'Samples',
          params: {
            type: requestType.value.toLocaleLowerCase()
          }
        });
      }
    }
    isInProgress.value = false;
  }
  const searchValue = ref('');
  const debouncedLoadFromServerProject = useDebounceFn(async () => {
    await loadProjectsFromServer();
  }, 350);
  const debouncedProjectSearch = useDebounceFn(() => {
    const search = searchValue.value.length < 1 ? undefined : searchValue.value;
    if (search) {
      allProjectsOptionsPage.value.all_projects.search = search;
      allProjectsOptionsPage.value.all_projects.search_columns = ['name', 'departments__shortcut'];
    } else {
      allProjectsOptionsPage.value.all_projects.search = '';
      allProjectsOptionsPage.value.all_projects.search_columns = [];
    }
  }, 350);

  watch(
    () => [allProjectsOptionsPage.value.all_projects.search],
    async () => {
      await debouncedLoadFromServerProject();
    }
  );

  watch(searchValue, () => {
    debouncedProjectSearch();
  });

  const loadProjectsFromServer = async () => {
    allProjectsOptionsPage.value.all_projects.options.rowsPerPage = 1000;
    await projectsStore.projectPageGetAllProjects();
  };

  watch(
    () => selectedStandard.value,
    () => {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.samples = [];
        if (selectedStandard.value && selectedStandard.value.analytical_techniques) {
          for (const technique of selectedStandard.value.analytical_techniques) {
            if (projectTechniques.value && projectTechniques.value.length > 0) {
              if (
                projectTechniques.value.find(
                  (t) => t.analytical_technique_id === technique.analytical_technique_id
                )
              ) {
                const techniqueData = projectTechniques.value.find(
                  (t) => t.analytical_technique_id === technique.analytical_technique_id
                );
                if (techniqueData) {
                  newGenerateAnalyticalRequestData.value.samples.push({
                    technique_id: technique.analytical_technique_id,
                    notes: ''
                  });
                }
              }
            }
          }
        }
      }
    }
  );

  const searchValueBatch = ref('');
  const debouncedBatchSearch = useDebounceFn(async () => {
    if (searchValueBatch.value !== '') {
      if (selectedDepartment.value && selectedDepartment.value.project_department_id) {
        batchesOptions.value.search = searchValueBatch.value;
        batchesOptions.value.search_columns = ['batch_number'];
        batchesOptions.value.filterOptions = [
          { column: 'project_department_id', value: selectedDepartment.value.project_department_id }
        ];
      }
      await loadBatchFromServer();
    }
  }, 350);

  watch(searchValueBatch, () => {
    debouncedBatchSearch();
  });

  watch(
    () => newGenerateAnalyticalRequestData.value?.batch_number,
    async () => {
      await debouncedSampleSearch();
    }
  );

  const loadBatchFromServer = async () => {
    await analyticalRequestsStore.getBatches();
  };
  const searchValueSample = ref('');
  const debouncedSampleSearch = useDebounceFn(async () => {
    if (searchValueSample.value !== '') {
      if (
        selectedDepartment.value &&
        selectedDepartment.value.project_department_id &&
        newGenerateAnalyticalRequestData.value?.batch_number
      ) {
        SampleOptions.search = searchValueSample.value;
        SampleOptions.fixedFilterOptions = [
          {
            column: 'project_department_id',
            value: selectedDepartment.value.project_department_id
          },
          {
            column: 'batch_number__batch_number',
            value: newGenerateAnalyticalRequestData.value?.batch_number
          }
        ];
        await analyticalRequestsStore.getAnalyticalRequests(true, SampleOptions, 'OR');
      }
    }
  }, 350);

  const debouncedSampleSearchEmpty = async () => {
    if (
      selectedDepartment.value &&
      selectedDepartment.value.project_department_id &&
      newGenerateAnalyticalRequestData.value?.batch_number
    ) {
      SampleOptions.search = '';
      SampleOptions.fixedFilterOptions = [
        { column: 'project_department_id', value: selectedDepartment.value.project_department_id },
        {
          column: 'batch_number__batch_number',
          value: newGenerateAnalyticalRequestData.value?.batch_number
        }
      ];
      await analyticalRequestsStore.getAnalyticalRequests(true, SampleOptions, 'OR');
    }
  };

  watch(searchValueSample, () => {
    debouncedSampleSearch();
  });

  const dynamicNoDataTextBatch = computed(() => {
    if (selectedDepartment.value && selectedDepartment.value.project_department_id) {
      return 'Žádná další políčka';
    } else {
      return 'Vyberte nejdříve oddělení';
    }
  });

  const dynamicNoDataTextSample = computed(() => {
    if (
      newGenerateAnalyticalRequestData.value &&
      !newGenerateAnalyticalRequestData.value?.batch_number
    ) {
      return 'Vyberte nejdříve číslo šarže';
    } else {
      return 'Žádná další políčka';
    }
  });

  const getAllFiles = async (file_id: number) => {
    if (newGenerateAnalyticalRequestData.value && newGenerateAnalyticalRequestData.value.files) {
      const foundFile = newGenerateAnalyticalRequestData.value.files.find(
        (f) => f.file_id === file_id
      );

      if (foundFile && newGenerateAnalyticalRequestData.value.files.indexOf(foundFile) > -1) {
        newGenerateAnalyticalRequestData.value.files.splice(
          newGenerateAnalyticalRequestData.value.files.indexOf(foundFile),
          1
        );
      }
    }
  };

  watch(
    [
      selectedProject,
      standardType,
      selectedDepartment,
      newGenerateAnalyticalRequestData,
      selectedUser
    ],
    () => {
      const newData = {
        project: selectedProject.value,
        type:
          standardType.value?.standard_type === null
            ? NewAnalyticalRequestType.NORMAL
            : standardType.value?.standard_type === 'rd'
              ? NewAnalyticalRequestType.STANDARD_RD
              : NewAnalyticalRequestType.STANDARD_QC,
        project_department: selectedDepartment.value,
        sample_number: newGenerateAnalyticalRequestData.value?.sample_number,
        batch_number: newGenerateAnalyticalRequestData.value?.batch_number,
        name: newGenerateAnalyticalRequestData.value?.name,
        analytical_department_request_type: standardType.value?.standard_type,
        samples: newGenerateAnalyticalRequestData.value?.samples,
        files_ids: newGenerateAnalyticalRequestData.value
          ? newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id)
          : [],
        user_id: selectedUser.value
      };
      localStorage.setItem('newAnalyticalRequestExternalStore', JSON.stringify(newData));
    },
    { deep: true }
  );

  const batchNumber = ref<string | null>(
    newGenerateAnalyticalRequestData.value?.batch_number || null
  );
  const sampleNumber = ref<string | null>(
    newGenerateAnalyticalRequestData.value?.sample_number || null
  );
  const onSelectBatch = async (selectedBatchNumber: BatchNumbersDto | string) => {
    if (!selectedBatchNumber) {
      return;
    }
    if (typeof selectedBatchNumber === 'string') {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.batch_number = selectedBatchNumber;
      }
      return;
    }
    if (newGenerateAnalyticalRequestData.value) {
      newGenerateAnalyticalRequestData.value.batch_number = selectedBatchNumber.batch_number;
      await debouncedSampleSearchEmpty();
    }
  };

  const onSelectSample = async (selectedSampleNumber: AnalyticalRequestDto | string) => {
    const formatRegex = /^\d+\/\d+[A-Z]*$/;
    const cleanSampleRegex = /^(\d+\/\d+)\/[QS]$/;

    if (!selectedSampleNumber) {
      return;
    }

    let sampleNumberToCheck: string;

    if (typeof selectedSampleNumber === 'string') {
      sampleNumberToCheck = selectedSampleNumber;
    } else {
      sampleNumberToCheck = selectedSampleNumber.sample_number;
    }

    const match = sampleNumberToCheck.match(cleanSampleRegex);
    if (match) {
      sampleNumberToCheck = match[1];
    }

    if (!formatRegex.test(sampleNumberToCheck)) {
      notification.error({
        message: 'Špatný formát čísla vzorku',
        description: 'Prosím použijte formát číslo/znaky.'
      });
      sampleNumber.value = null;
      return;
    }

    if (typeof selectedSampleNumber === 'string') {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.sample_number = sampleNumberToCheck;
      }
    } else {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.sample_number = sampleNumberToCheck;
      }
    }
  };
</script>
